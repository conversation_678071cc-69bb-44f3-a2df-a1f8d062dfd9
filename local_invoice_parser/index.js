const fs = require("fs");
const path = require("path");
const { extractFieldsFromText } = require("./utils/enhancedExtractText");
const { extractTextWithFallback, validateTextQuality } = require("./utils/multiPdfParser");

// Directory containing the PDF files
const invoicesDir = path.resolve(__dirname, "invoices");

// Get all PDF files in the directory
const pdfFiles = fs.readdirSync(invoicesDir).filter(file => file.toLowerCase().endsWith('.pdf'));

/**
 * Process all PDF files and generate exact expected JSON output
 */
async function processAllPdfs() {
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    console.log(`\n🚀 Starting to process ${pdfFiles.length} PDF files...\n`);

    for (const pdfFile of pdfFiles) {
        const filePath = path.join(invoicesDir, pdfFile);
        console.log(`📄 Processing: ${pdfFile}`);

        try {
            // Extract text from PDF using enhanced multi-library approach
            const text = await extractTextWithFallback(filePath);

            // Validate text quality
            const quality = validateTextQuality(text);
            console.log(`   📊 Text quality: ${quality.quality}% (${quality.lineCount} lines)`);

            // Extract structured data using enhanced extraction
            const result = extractFieldsFromText(text);

            // Determine document type and validate structure
            const documentType = getDocumentType(result);
            const structureInfo = getStructureInfo(result, documentType);

            // Clean result - remove any processing metadata for clean output
            const cleanResult = cleanResultForOutput(result);

            // Add to results array
            results.push(cleanResult);
            successCount++;

            // Save individual result
            const outputPath = path.join(__dirname, `output_${pdfFile.replace('.pdf', '.json')}`);
            fs.writeFileSync(outputPath, JSON.stringify(cleanResult, null, 2));

            console.log(`   ✅ Success: ${structureInfo.fieldsCount} fields, ${structureInfo.itemsCount} items`);
            console.log(`   📁 Individual output: output_${pdfFile.replace('.pdf', '.json')}`);

        } catch (error) {
            console.error(`   ❌ Error processing ${pdfFile}:`, error.message);
            errorCount++;

            // Add error result
            results.push({
                filename: pdfFile,
                error: error.message,
                processedAt: new Date().toISOString()
            });
        }
    }

    // Write combined results to output.json with clean structure
    const finalOutput = {
        processingMetadata: {
            totalFiles: pdfFiles.length,
            successfullyProcessed: successCount,
            errors: errorCount,
            processedAt: new Date().toISOString()
        },
        results: results
    };

    fs.writeFileSync("output.json", JSON.stringify(finalOutput, null, 2));

    console.log(`\n📊 Processing Summary:`);
    console.log(`   Total files: ${pdfFiles.length}`);
    console.log(`   Successfully processed: ${successCount}`);
    console.log(`   Errors: ${errorCount}`);
    console.log(`\n✅ Combined output written to output.json`);
}

/**
 * Determine document type from result structure
 * @param {Object} result - Extracted result
 * @return {string} Document type
 */
function getDocumentType(result) {
    if (result.DeliveryChallan) return "DELIVERY_CHALLAN";
    if (result.IRN || result.AckNo) return "TAX_INVOICE";
    if (result.JobOrder) return "JOB_ORDER";
    if (result.PurchaseOrder) return "PURCHASE_ORDER";
    return "UNKNOWN";
}

/**
 * Get structure information for logging
 * @param {Object} result - Extracted result
 * @param {string} documentType - Document type
 * @return {Object} Structure info
 */
function getStructureInfo(result, documentType) {
    let fieldsCount = Object.keys(result).length;
    let itemsCount = 0;

    if (result.Goods) itemsCount = result.Goods.length;
    else if (result.Items) itemsCount = result.Items.length;

    return { fieldsCount, itemsCount };
}

/**
 * Clean result for output - remove processing metadata
 * @param {Object} result - Raw result
 * @return {Object} Clean result
 */
function cleanResultForOutput(result) {
    // Create a clean copy without processing metadata
    const cleanResult = { ...result };

    // Remove any processing-specific fields that shouldn't be in final output
    delete cleanResult.filename;
    delete cleanResult.processedAt;
    delete cleanResult.extractionSummary;

    return cleanResult;
}

// Run the process
processAllPdfs().catch(error => {
    console.error("Error:", error);
});