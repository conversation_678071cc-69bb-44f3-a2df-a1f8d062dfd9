/**
 * Enhanced multi-library PDF parser for superior text extraction accuracy
 * Uses multiple PDF parsing libraries with intelligent fallback mechanisms
 */

const fs = require('fs');
const pdf = require('pdf-parse');

/**
 * Extract text using enhanced multi-library approach with better error handling
 * @param {string} filePath - Path to PDF file
 * @return {Promise<string>} Extracted text
 */
async function extractTextWithFallback(filePath) {
    const dataBuffer = fs.readFileSync(filePath);

    try {
        // Primary method: pdf-parse with optimized settings
        const data = await pdf(dataBuffer, {
            normalizeWhitespace: true,
            disableCombineTextItems: false
        });

        let text = data.text;

        // Enhanced preprocessing for better parsing
        text = enhancedPreprocessing(text);

        // Validate extraction quality
        const quality = validateTextQuality(text);

        // If quality is too low, try alternative extraction
        if (quality.quality < 50) {
            console.log('   🔄 Low quality detected, trying alternative extraction...');
            const alternativeText = await tryAlternativeExtraction(dataBuffer);
            if (alternativeText && validateTextQuality(alternativeText).quality > quality.quality) {
                return enhancedPreprocessing(alternativeText);
            }
        }

        return text;

    } catch (error) {
        console.error('Primary PDF extraction failed:', error.message);

        // Enhanced fallback with multiple options
        try {
            const alternativeText = await tryAlternativeExtraction(dataBuffer);
            return enhancedPreprocessing(alternativeText);
        } catch (fallbackError) {
            console.error('All extraction methods failed:', fallbackError.message);
            return '';
        }
    }
}

/**
 * Enhanced alternative PDF extraction methods with multiple fallbacks
 * @param {Buffer} dataBuffer - PDF file buffer
 * @return {Promise<string>} Extracted text
 */
async function tryAlternativeExtraction(dataBuffer) {
    // Method 1: Try pdf2json for structured extraction
    try {
        const pdf2json = require('pdf2json');
        const text = await extractWithPdf2json(dataBuffer);
        if (text && text.length > 100) {
            return text;
        }
    } catch (error) {
        console.log('   📄 pdf2json extraction failed, trying next method...');
    }

    // Method 2: Try pdf-parse with different options
    try {
        const data = await pdf(dataBuffer, {
            normalizeWhitespace: false,
            disableCombineTextItems: true
        });
        if (data.text && data.text.length > 100) {
            return data.text;
        }
    } catch (error) {
        console.log('   📄 Alternative pdf-parse failed, trying final method...');
    }

    // Method 3: Try with minimal processing
    try {
        const data = await pdf(dataBuffer, {
            normalizeWhitespace: true,
            disableCombineTextItems: false,
            max: 1 // Process only first page if needed
        });
        return data.text || '';
    } catch (finalError) {
        console.error('   ❌ All PDF extraction methods failed:', finalError.message);
        return '';
    }
}

/**
 * Extract text using pdf2json
 * @param {Buffer} dataBuffer - PDF file buffer
 * @return {Promise<string>} Extracted text
 */
function extractWithPdf2json(dataBuffer) {
    return new Promise((resolve, reject) => {
        const PDFParser = require('pdf2json');
        const pdfParser = new PDFParser();
        
        pdfParser.on('pdfParser_dataError', errData => {
            reject(new Error(errData.parserError));
        });
        
        pdfParser.on('pdfParser_dataReady', pdfData => {
            try {
                let text = '';
                
                // Extract text from each page
                pdfData.formImage.Pages.forEach(page => {
                    page.Texts.forEach(textItem => {
                        textItem.R.forEach(run => {
                            text += decodeURIComponent(run.T) + ' ';
                        });
                        text += '\n';
                    });
                });
                
                resolve(text);
            } catch (error) {
                reject(error);
            }
        });
        
        pdfParser.parseBuffer(dataBuffer);
    });
}

/**
 * Enhanced text preprocessing for superior parsing accuracy
 * @param {string} text - Raw extracted text
 * @return {string} Preprocessed text
 */
function enhancedPreprocessing(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    // Step 1: Normalize line endings
    let processed = text.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Step 2: Fix common OCR issues but preserve line structure
    processed = processed.replace(/[ \t]+/g, ' '); // Multiple spaces to single space within lines

    // Step 3: Fix field separators and formatting (preserve line breaks and important identifiers)
    const lines = processed.split('\n');
    processed = lines.map(line => {
        // Skip processing for lines that contain important identifiers
        if (line.match(/RSNT-[A-Z0-9\-]+|GSTIN|PAN|IRN|[A-Z0-9]{15}|[A-Z0-9]{10}/)) {
            return line; // Don't modify lines with important codes
        }

        // Apply transformations per line to avoid breaking structure
        line = line.replace(/([a-z])([A-Z])/g, '$1 $2'); // Add space between camelCase
        // Be more selective about adding spaces between letters and numbers
        line = line.replace(/([a-zA-Z])(\d{4,})/g, '$1 $2'); // Only for longer numbers
        line = line.replace(/(\d{4,})([a-zA-Z])/g, '$1 $2'); // Only for longer numbers
        return line;
    }).join('\n');

    // Step 4: Remove leading/trailing spaces from each line
    processed = processed.split('\n').map(line => line.trim()).join('\n');

    // Step 5: Remove excessive empty lines but preserve structure
    processed = processed.replace(/\n{3,}/g, '\n\n');

    return processed;
}

/**
 * Enhanced text quality validation with comprehensive metrics
 * @param {string} text - Extracted text
 * @return {Object} Quality metrics
 */
function validateTextQuality(text) {
    if (!text || typeof text !== 'string') {
        return {
            lineCount: 0,
            hasCompanyName: false,
            hasGSTIN: false,
            hasItemCodes: false,
            hasInvoiceFields: false,
            hasAddressInfo: false,
            quality: 0
        };
    }

    const lines = text.split('\n').filter(line => line.trim().length > 0);

    // Enhanced quality checks
    const hasCompanyName = /Resonate Systems Private Limited/i.test(text);
    const hasGSTIN = /GSTIN\/UIN\s*:\s*[0-9A-Z]{15}/i.test(text) || /GSTIN\s*[:\-]?\s*[0-9A-Z]{15}/i.test(text);
    const hasItemCodes = /RSNT-[A-Z0-9\-]+/i.test(text) || /EUPS-[A-Z0-9\-]+/i.test(text);
    const hasInvoiceFields = /Invoice\s*No|Delivery\s*Note|Purchase\s*Order/i.test(text);
    const hasAddressInfo = /Consignee|Buyer|Ship\s*to|Bill\s*to/i.test(text);
    const hasPAN = /PAN\/IT\s*No|PAN\s*Number/i.test(text);
    const hasAmounts = /\d+\.\d{2}/.test(text);

    // Calculate quality score (0-100)
    let qualityScore = 0;
    if (hasCompanyName) qualityScore += 20;
    if (hasGSTIN) qualityScore += 20;
    if (hasItemCodes) qualityScore += 15;
    if (hasInvoiceFields) qualityScore += 15;
    if (hasAddressInfo) qualityScore += 10;
    if (hasPAN) qualityScore += 10;
    if (hasAmounts) qualityScore += 5;
    if (lines.length > 20) qualityScore += 5;

    return {
        lineCount: lines.length,
        hasCompanyName,
        hasGSTIN,
        hasItemCodes,
        hasInvoiceFields,
        hasAddressInfo,
        quality: Math.min(qualityScore, 100)
    };
}

module.exports = {
    extractTextWithFallback,
    enhancedPreprocessing,
    validateTextQuality
};